<template>
	<view class="container">
		<!-- 自定义头部 -->
		<view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="header-content">
				<view class="header-left"></view>
				<view class="header-center">
					<text class="title">热量规划</text>
				</view>
			</view>
		</view>

		<!-- 页面内容 -->
		<view class="page-content">
			<!-- 每日热量计划 -->
			<view class="plan-section">
				<view class="section-header">
					<text class="section-title">每日热量计划</text>
					<view class="calculate-btn">
						<image class="compute-icon" src="/static/images/calorie_plan/icon-compute.svg" mode="aspectFit">
						</image>
						<text class="btn-text">为我计算</text>
					</view>
				</view>

				<view class="calorie-display">
					<text class="calorie-number">1469</text>
					<text class="calorie-unit">千卡/天</text>
				</view>

				<view class="progress-container">
					<view class="progress-bar">
						<view class="progress-fill"></view>
					</view>
					<text class="progress-text">每日缺口：367千卡</text>
				</view>

				<!-- 基础代谢信息 -->
				<view class="metabolism-section">
					<text class="metabolism-label">您的日常基础代谢：</text>
					<text class="metabolism-value">1836千卡/天</text>
					<view class="user-info">
						<text class="info-item info-height">身高：175cm</text>
						<text class="info-item info-weight">体重：70.3kg</text>
						<text class="info-item info-age">年龄：100</text>
						<text class="info-item info-gender">性别：未知</text>
					</view>
				</view>

				<!-- 减重模式选择 -->
				<view class="section-header-row">
					<text class="section-title">减重模式选择</text>
					<text class="section-subtitle">(修改热量缺口)</text>
				</view>

				<view class="mode-options">
					<view class="mode-option">
						<text class="mode-name">快速减重</text>
						<text class="mode-desc">-1.25kg/周</text>
					</view>
					<view class="mode-option active">
						<text class="mode-name">平稳减重</text>
						<text class="mode-desc">-0.83kg/周</text>
					</view>
					<view class="mode-option">
						<text class="mode-name">体重维持</text>
						<text class="mode-desc">-0kg/周</text>
					</view>
					<view class="mode-option">
						<text class="mode-name">自定义</text>
						<text class="mode-desc">-0kg/周</text>
					</view>
				</view>
			</view>

			<!-- 体重预测 -->
			<view class="prediction-section">
				<text class="section-title weight-prediction-title">体重预测</text>

				<view class="chart-container">
					<view class="chart">
						<view class="chart-bar" style="height: 80%;">
							<text class="bar-value">70.0kg</text>
							<text class="bar-label">当前</text>
						</view>
						<view class="chart-bar" style="height: 70%;">
							<text class="bar-value">68.8kg</text>
							<text class="bar-label">1周</text>
						</view>
						<view class="chart-bar" style="height: 60%;">
							<text class="bar-value">65.0kg</text>
							<text class="bar-label">4周</text>
						</view>
						<view class="chart-bar" style="height: 45%;">
							<text class="bar-value">55.0kg</text>
							<text class="bar-label">12周</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0
			}
		},
		onLoad() {
			this.getSystemInfo()
		},
		methods: {
			getSystemInfo() {
				uni.getSystemInfo({
					success: (res) => {
						this.statusBarHeight = res.statusBarHeight || 20
					}
				})
			}
		}
	}
</script>

<style scoped>
	.container {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 120rpx;
	}

	/* 自定义头部 */
	.custom-header {
		background: linear-gradient(to bottom, #E3FFFA, #F2F2F2);
		position: static;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		padding-bottom: 30rpx;
	}

	.header-content {
		height: 120rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 30rpx;
		position: relative;
	}

	.header-left {
		min-width: 120rpx;
	}

	.header-center {
		position: absolute;
		left: 50%;
		top: 104rpx;
		transform: translate(-50%, -50%);
	}

	.title {
		font-size: 40rpx;
		font-weight: 600;
		color: #333333;
		letter-spacing: 1rpx;
		font-family: "PingFang SC-Bold", "Microsoft YaHei", sans-serif;
	}



	/* 页面内容 */
	.page-content {
		padding-top: 20rpx;
	}

	/* 计划部分 */
	.plan-section {
		background-color: #ffffff;
		margin: 0 20rpx 20rpx 20rpx;
		padding: 40rpx 30rpx;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	}
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
	}
	
	.section-header-row {
		display: flex;
		align-items: center;
		margin-top: 48rpx;
		margin-bottom: 30rpx;
	}

	.section-title {
		width: 168rpx;
		height: 40rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 28rpx;
		color: #343738;
		line-height: 40rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}

	.weight-prediction-title {
		width: 128rpx;
		height: 40rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 600;
		font-size: 32rpx;
		color: #343738;
		line-height: 40rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}
	
	.calculate-btn {
		width: 164rpx;
		height: 52rpx;
		background: #FFFFFF;
		border-radius: 18rpx;
		border: 1rpx solid #13CEAD;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
		gap: 8rpx;
	}

	.compute-icon {
		width: 24rpx;
		height: 24rpx;
		flex-shrink: 0;
	}

	.btn-text {
		width: 96rpx;
		height: 36rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #13CEAD;
		line-height: 36rpx;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}
	
	.calorie-display {
		display: flex;
		align-items: flex-end;
		justify-content: flex-start;
		margin-bottom: 30rpx;
		gap: 8rpx;
	}
	
	.calorie-number {
		width: 174rpx;
		height: 72rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 72rpx;
		color: #13CEAD;
		line-height: 72rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}
	
	.calorie-unit {
		width: 84rpx;
		height: 36rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #343738;
		line-height: 36rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}
	
	.progress-container {
		text-align: center;
	}
	
	.progress-bar {
		width: 100%;
		height: 8rpx;
		background-color: #E5E5E5;
		border-radius: 4rpx;
		overflow: hidden;
		margin-bottom: 20rpx;
	}
	
	.progress-fill {
		width: 75%;
		height: 100%;
		background-color: #00D4AA;
	}
	
	.progress-text {
		font-size: 28rpx;
		color: #00D4AA;
	}
	
	/* 基础代谢部分 */
	.metabolism-section {
		background-color: #00D4AA;
		background-image: url('/static/images/calorie_plan/icon-daily-bmr@3x copy.png');
		background-size: contain;
		background-repeat: no-repeat;
		background-position: right center;
		margin: 30rpx 0 20rpx 0;
		padding: 40rpx 30rpx;
		border-radius: 0;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	}

	.metabolism-label {
		width: 252rpx;
		height: 40rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 28rpx;
		color: #FFFFFF;
		line-height: 40rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}

	.metabolism-value {
		width: 176rpx;
		height: 40rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 28rpx;
		color: #FFFFFF;
		line-height: 40rpx;
		text-align: left;
		font-style: normal;
		text-decoration-line: underline;
		text-transform: none;
	}
	
	.metabolism-title {
		font-size: 32rpx;
		color: #ffffff;
		font-weight: bold;
		display: block;
		margin-bottom: 20rpx;
	}
	
	.user-info {
		display: flex;
		justify-content: space-between;
		margin-top: 24rpx;
	}
	
	.info-item {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #FFFFFF;
		line-height: 36rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
		height: 36rpx;
		white-space: nowrap;
	}

	.info-height {
		width: 136rpx;
	}

	.info-weight {
		width: 130rpx;
	}

	.info-age {
		width: 100rpx;
	}

	.info-gender {
		width: 100rpx;
	}
	
	/* 模式选择部分 */
	.mode-section {
		background-color: #ffffff;
		margin: 20rpx 0 0 0;
		padding: 30rpx;
		border-radius: 16rpx;
	}
	
	.section-subtitle {
		width: 160rpx;
		height: 36rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 24rpx;
		color: #666666;
		line-height: 36rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
		margin-left: 12rpx;
	}
	
	.mode-options {
		display: flex;
		justify-content: space-between;
		margin-top: 30rpx;
		gap: 8rpx;
		background-color: #ffffff;
	}
	
	.mode-option {
		width: 144rpx;
		height: 144rpx;
		background: #F8F8F8;
		border-radius: 16rpx;
		flex: 1;
		padding: 20rpx;
		text-align: center;
		border: 2rpx solid transparent;
	}

	.mode-option:first-child {
		margin-left: 0rpx;
	}

	.mode-option:last-child {
		margin-right: 0rpx;
	}
	
	.mode-option.active {
		width: 144rpx;
		height: 144rpx;
		background: #13CEAD;
		border-radius: 16rpx;
	}
	
	.mode-name {
		width: 112rpx;
		height: 40rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 28rpx;
		color: #343738;
		line-height: 40rpx;
		text-align: center;
		font-style: normal;
		text-transform: none;
		display: block;
		margin-bottom: 10rpx;
	}
	
	.mode-desc {
		width: 108rpx;
		height: 32rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 24rpx;
		color: #343738;
		line-height: 32rpx;
		text-align: center;
		font-style: normal;
		text-transform: none;
		padding-left: 8rpx;
		padding-right: 8rpx;
		white-space: nowrap;
	}

	.mode-option.active .mode-name {
		width: 112rpx;
		height: 40rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 28rpx;
		color: #FFFFFF;
		line-height: 40rpx;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}

	.mode-option.active .mode-desc {
		width: 108rpx;
		height: 32rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 24rpx;
		color: #FFFFFF;
		line-height: 32rpx;
		text-align: center;
		font-style: normal;
		text-transform: none;
		padding-left: 8rpx;
		padding-right: 8rpx;
		white-space: nowrap;
	}
	
	/* 体重预测部分 */
	.prediction-section {
		background-color: #ffffff;
		margin: 0 20rpx 20rpx 20rpx;
		padding: 30rpx;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	}
	
	.chart-container {
		margin-top: 30rpx;
	}
	
	.chart {
		display: flex;
		justify-content: space-between;
		align-items: flex-end;
		height: 400rpx;
		padding: 20rpx 0;
	}
	
	.chart-bar {
		flex: 1;
		background-color: #4A90E2;
		margin: 0 10rpx;
		border-radius: 8rpx 8rpx 0 0;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		align-items: center;
		padding: 20rpx 10rpx;
		position: relative;
	}
	
	.bar-value {
		font-size: 24rpx;
		color: #ffffff;
		font-weight: bold;
		margin-bottom: 10rpx;
	}
	
	.bar-label {
		font-size: 24rpx;
		color: #333333;
		position: absolute;
		bottom: -40rpx;
	}
</style>
